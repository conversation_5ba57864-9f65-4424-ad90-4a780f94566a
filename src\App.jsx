import { createHashRouter, RouterProvider } from "react-router-dom";
import "./App.css";
import LayOut from "./Components/LayOut/LayOut";
import Home from "./Components/Home/Home";
import About from "./Components/About/About";
import Contacts from "./Components/Contacts/Contacts";
import NotFound from "./Components/NotFound/NotFound";

const routes = createHashRouter([
  {
    path: "",
    element: <LayOut />,
    children: [
      { index: true, element: <Home /> },
      { path: "About", element: <About /> },
      { path: "Contacts", element: <Contacts /> },
      { path: "*", element: <NotFound /> },
    ],
  },
]);
function App() {
  return (
    <>
      <RouterProvider router={routes}></RouterProvider>
    </>
  );
}

export default App;
